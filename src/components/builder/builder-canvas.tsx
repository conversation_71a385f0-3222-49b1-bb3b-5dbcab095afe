'use client'

import { useDroppable } from '@dnd-kit/core'
import { Card } from '@/components/ui/card'
import { useUIBuilderStore } from '@/lib/store'
import { CanvasComponent } from './canvas-component'

export function BuilderCanvas() {
  const { currentProject, selectedComponent, setSelectedComponent } = useUIBuilderStore()
  
  const { setNodeRef, isOver } = useDroppable({
    id: 'canvas',
  })

  const components = currentProject?.components || []

  const handleCanvasClick = (e: React.MouseEvent) => {
    // Only deselect if clicking on the canvas itself, not on a component
    if (e.target === e.currentTarget) {
      setSelectedComponent(null)
    }
  }

  return (
    <div className="h-full p-4">
      <div className="h-full max-w-4xl mx-auto">
        <div
          ref={setNodeRef}
          onClick={handleCanvasClick}
          className={`
            relative h-full min-h-[600px] bg-white dark:bg-gray-800 rounded-lg border-2 border-dashed
            ${isOver 
              ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 dark:border-gray-600'
            }
            transition-colors duration-200
          `}
        >
          {/* Canvas Grid Background */}
          <div 
            className="absolute inset-0 opacity-30"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />

          {/* Empty State */}
          {components.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Start Building
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                  Drag components from the palette on the left to start building your interface
                </p>
              </div>
            </div>
          )}

          {/* Render Components */}
          {components.map((component) => (
            <CanvasComponent
              key={component.id}
              component={component}
              isSelected={selectedComponent === component.id}
              onSelect={() => setSelectedComponent(component.id)}
            />
          ))}

          {/* Selection Indicator */}
          {isOver && (
            <div className="absolute inset-0 border-2 border-blue-400 rounded-lg pointer-events-none" />
          )}
        </div>
      </div>
    </div>
  )
}
