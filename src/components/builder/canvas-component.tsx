'use client'

import { useDraggable } from '@dnd-kit/core'
import { UIComponent } from '@/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { useUIBuilderStore } from '@/lib/store'

interface CanvasComponentProps {
  component: UIComponent
  isSelected: boolean
  onSelect: () => void
}

export function CanvasComponent({ component, isSelected, onSelect }: CanvasComponentProps) {
  const { updateComponent, removeComponent } = useUIBuilderStore()

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: component.id,
    data: {
      component,
      fromCanvas: true,
    },
  })

  const style = {
    position: 'absolute' as const,
    left: component.position.x,
    top: component.position.y,
    width: component.size.width,
    minHeight: component.size.height,
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
    zIndex: isDragging ? 1000 : component.position.z || 1,
  }

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect()
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    removeComponent(component.id)
  }

  const renderComponent = () => {
    switch (component.type) {
      case 'button':
        return (
          <Button 
            variant={component.props.variant || 'default'}
            size={component.props.size || 'default'}
            className="w-full"
          >
            {component.props.text || 'Button'}
          </Button>
        )

      case 'input':
        return (
          <Input
            placeholder={component.props.placeholder || 'Enter text...'}
            type={component.props.type || 'text'}
            className="w-full"
          />
        )

      case 'textarea':
        return (
          <Textarea
            placeholder={component.props.placeholder || 'Enter text...'}
            className="w-full"
            rows={component.props.rows || 3}
          />
        )

      case 'text':
        return (
          <p 
            className={`text-${component.props.fontSize || 'base'} ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Text content'}
          </p>
        )

      case 'heading':
        const HeadingTag = `h${component.props.level || 1}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag 
            className={`font-bold ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Heading'}
          </HeadingTag>
        )

      case 'card':
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{component.props.title || 'Card Title'}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{component.props.content || 'Card content goes here...'}</p>
            </CardContent>
          </Card>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox id={component.id} />
            <Label htmlFor={component.id}>
              {component.props.label || 'Checkbox label'}
            </Label>
          </div>
        )

      case 'container':
        return (
          <div 
            className={`border-2 border-dashed border-gray-300 rounded p-4 ${component.props.className || ''}`}
            style={{ 
              backgroundColor: component.props.backgroundColor,
              minHeight: '100px'
            }}
          >
            <div className="text-center text-gray-500 text-sm">
              Container
            </div>
          </div>
        )

      default:
        return (
          <div className="border border-gray-300 rounded p-2 bg-gray-100">
            <Badge variant="secondary">{component.type}</Badge>
          </div>
        )
    }
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      onClick={handleClick}
      className={`
        group cursor-pointer
        ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
        ${isDragging ? 'opacity-50' : ''}
      `}
    >
      {/* Component Content */}
      <div className="relative">
        {renderComponent()}
        
        {/* Selection Controls */}
        {isSelected && (
          <div className="absolute -top-8 left-0 flex items-center gap-1 bg-blue-500 text-white px-2 py-1 rounded text-xs">
            <span>{component.type}</span>
            <button
              onClick={handleDelete}
              className="ml-2 hover:bg-blue-600 rounded px-1"
            >
              ×
            </button>
          </div>
        )}

        {/* Drag Handle */}
        <div
          {...listeners}
          {...attributes}
          className="absolute inset-0 cursor-move opacity-0 group-hover:opacity-100 transition-opacity"
        />
      </div>
    </div>
  )
}
