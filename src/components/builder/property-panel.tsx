'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useUIBuilderStore } from '@/lib/store'
import { Badge } from '@/components/ui/badge'

export function PropertyPanel() {
  const { currentProject, selectedComponent, updateComponent } = useUIBuilderStore()

  const selectedComponentData = currentProject?.components.find(
    (comp) => comp.id === selectedComponent
  )

  const handlePropertyChange = (property: string, value: any) => {
    if (!selectedComponent) return
    
    updateComponent(selectedComponent, {
      props: {
        ...selectedComponentData?.props,
        [property]: value,
      },
    })
  }

  const handlePositionChange = (axis: 'x' | 'y', value: number) => {
    if (!selectedComponent || !selectedComponentData) return
    
    updateComponent(selectedComponent, {
      position: {
        ...selectedComponentData.position,
        [axis]: value,
      },
    })
  }

  const handleSizeChange = (dimension: 'width' | 'height', value: number) => {
    if (!selectedComponent || !selectedComponentData) return
    
    updateComponent(selectedComponent, {
      size: {
        ...selectedComponentData.size,
        [dimension]: value,
      },
    })
  }

  if (!selectedComponent || !selectedComponentData) {
    return (
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-4">Properties</h2>
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.121 2.122" />
            </svg>
          </div>
          <h3 className="text-lg font-medium mb-2">No Component Selected</h3>
          <p className="text-muted-foreground text-sm">
            Select a component on the canvas to edit its properties
          </p>
        </div>
      </div>
    )
  }

  const renderComponentProperties = () => {
    switch (selectedComponentData.type) {
      case 'button':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="button-text">Text</Label>
              <Input
                id="button-text"
                value={selectedComponentData.props.text || ''}
                onChange={(e) => handlePropertyChange('text', e.target.value)}
                placeholder="Button text"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="button-variant">Variant</Label>
              <Select
                value={selectedComponentData.props.variant || 'default'}
                onValueChange={(value) => handlePropertyChange('variant', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="destructive">Destructive</SelectItem>
                  <SelectItem value="outline">Outline</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="ghost">Ghost</SelectItem>
                  <SelectItem value="link">Link</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="button-size">Size</Label>
              <Select
                value={selectedComponentData.props.size || 'default'}
                onValueChange={(value) => handlePropertyChange('size', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">Small</SelectItem>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="lg">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )

      case 'input':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="input-placeholder">Placeholder</Label>
              <Input
                id="input-placeholder"
                value={selectedComponentData.props.placeholder || ''}
                onChange={(e) => handlePropertyChange('placeholder', e.target.value)}
                placeholder="Enter placeholder text"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="input-type">Type</Label>
              <Select
                value={selectedComponentData.props.type || 'text'}
                onValueChange={(value) => handlePropertyChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="password">Password</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="tel">Phone</SelectItem>
                  <SelectItem value="url">URL</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )

      case 'text':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="text-content">Content</Label>
              <Textarea
                id="text-content"
                value={selectedComponentData.props.content || ''}
                onChange={(e) => handlePropertyChange('content', e.target.value)}
                placeholder="Enter text content"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="text-color">Color</Label>
              <Input
                id="text-color"
                type="color"
                value={selectedComponentData.props.color || '#000000'}
                onChange={(e) => handlePropertyChange('color', e.target.value)}
              />
            </div>
          </>
        )

      case 'heading':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="heading-content">Content</Label>
              <Input
                id="heading-content"
                value={selectedComponentData.props.content || ''}
                onChange={(e) => handlePropertyChange('content', e.target.value)}
                placeholder="Heading text"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="heading-level">Level</Label>
              <Select
                value={selectedComponentData.props.level?.toString() || '1'}
                onValueChange={(value) => handlePropertyChange('level', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">H1</SelectItem>
                  <SelectItem value="2">H2</SelectItem>
                  <SelectItem value="3">H3</SelectItem>
                  <SelectItem value="4">H4</SelectItem>
                  <SelectItem value="5">H5</SelectItem>
                  <SelectItem value="6">H6</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )

      default:
        return (
          <div className="text-center py-4">
            <p className="text-muted-foreground text-sm">
              Properties for {selectedComponentData.type} component coming soon
            </p>
          </div>
        )
    }
  }

  return (
    <div className="p-4 space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-2">Properties</h2>
        <Badge variant="secondary">{selectedComponentData.type}</Badge>
      </div>

      {/* Component Properties */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Component</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {renderComponentProperties()}
        </CardContent>
      </Card>

      {/* Position & Size */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Position & Size</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-2">
              <Label htmlFor="pos-x">X</Label>
              <Input
                id="pos-x"
                type="number"
                value={selectedComponentData.position.x}
                onChange={(e) => handlePositionChange('x', parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="pos-y">Y</Label>
              <Input
                id="pos-y"
                type="number"
                value={selectedComponentData.position.y}
                onChange={(e) => handlePositionChange('y', parseInt(e.target.value) || 0)}
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-2">
              <Label htmlFor="size-width">Width</Label>
              <Input
                id="size-width"
                type="number"
                value={selectedComponentData.size.width}
                onChange={(e) => handleSizeChange('width', parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="size-height">Height</Label>
              <Input
                id="size-height"
                type="number"
                value={selectedComponentData.size.height}
                onChange={(e) => handleSizeChange('height', parseInt(e.target.value) || 0)}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
