'use client'

import { useDraggable } from '@dnd-kit/core'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ComponentType } from '@/types'
import {
  MousePointer2,
  Type,
  Heading1,
  Square,
  Image,
  Table,
  BarChart3,
  Container,
  Grid3X3,
  Rows,
  CheckSquare,
  Circle,
  Upload
} from 'lucide-react'

interface ComponentItem {
  type: ComponentType
  label: string
  icon: React.ReactNode
  category: 'form' | 'display' | 'layout'
  description: string
}

const componentItems: ComponentItem[] = [
  // Form Components
  {
    type: 'button',
    label: 'Button',
    icon: <MousePointer2 className="w-4 h-4" />,
    category: 'form',
    description: 'Interactive button element'
  },
  {
    type: 'input',
    label: 'Input',
    icon: <Type className="w-4 h-4" />,
    category: 'form',
    description: 'Text input field'
  },
  {
    type: 'textarea',
    label: 'Textarea',
    icon: <Square className="w-4 h-4" />,
    category: 'form',
    description: 'Multi-line text input'
  },
  {
    type: 'select',
    label: 'Select',
    icon: <Square className="w-4 h-4" />,
    category: 'form',
    description: 'Dropdown selection'
  },
  {
    type: 'checkbox',
    label: 'Checkbox',
    icon: <CheckSquare className="w-4 h-4" />,
    category: 'form',
    description: 'Checkbox input'
  },
  {
    type: 'radio',
    label: 'Radio',
    icon: <Circle className="w-4 h-4" />,
    category: 'form',
    description: 'Radio button group'
  },
  {
    type: 'file-upload',
    label: 'File Upload',
    icon: <Upload className="w-4 h-4" />,
    category: 'form',
    description: 'File upload component'
  },

  // Display Components
  {
    type: 'text',
    label: 'Text',
    icon: <Type className="w-4 h-4" />,
    category: 'display',
    description: 'Plain text content'
  },
  {
    type: 'heading',
    label: 'Heading',
    icon: <Heading1 className="w-4 h-4" />,
    category: 'display',
    description: 'Heading text (H1-H6)'
  },
  {
    type: 'image',
    label: 'Image',
    icon: <Image className="w-4 h-4" />,
    category: 'display',
    description: 'Image display'
  },
  {
    type: 'card',
    label: 'Card',
    icon: <Square className="w-4 h-4" />,
    category: 'display',
    description: 'Card container'
  },
  {
    type: 'table',
    label: 'Table',
    icon: <Table className="w-4 h-4" />,
    category: 'display',
    description: 'Data table'
  },
  {
    type: 'chart',
    label: 'Chart',
    icon: <BarChart3 className="w-4 h-4" />,
    category: 'display',
    description: 'Data visualization'
  },

  // Layout Components
  {
    type: 'container',
    label: 'Container',
    icon: <Container className="w-4 h-4" />,
    category: 'layout',
    description: 'Generic container'
  },
  {
    type: 'grid',
    label: 'Grid',
    icon: <Grid3X3 className="w-4 h-4" />,
    category: 'layout',
    description: 'CSS Grid layout'
  },
  {
    type: 'flex',
    label: 'Flex',
    icon: <Rows className="w-4 h-4" />,
    category: 'layout',
    description: 'Flexbox layout'
  },
]

interface DraggableComponentProps {
  item: ComponentItem
}

function DraggableComponent({ item }: DraggableComponentProps) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `palette-${item.type}`,
    data: {
      fromPalette: true,
      componentType: item.type,
    },
  })

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`
        p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-grab
        hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
        ${isDragging ? 'opacity-50' : ''}
      `}
    >
      <div className="flex items-center gap-2 mb-1">
        {item.icon}
        <span className="font-medium text-sm">{item.label}</span>
      </div>
      <p className="text-xs text-muted-foreground">{item.description}</p>
    </div>
  )
}

export function ComponentPalette() {
  const categories = ['form', 'display', 'layout'] as const

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold mb-4">Components</h2>
      
      {categories.map((category) => (
        <div key={category} className="mb-6">
          <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide mb-3">
            {category} Components
          </h3>
          <div className="space-y-2">
            {componentItems
              .filter((item) => item.category === category)
              .map((item) => (
                <DraggableComponent key={item.type} item={item} />
              ))}
          </div>
        </div>
      ))}
    </div>
  )
}
