'use client'

import { useState } from 'react'
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCenter } from '@dnd-kit/core'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ComponentPalette } from './component-palette'
import { BuilderCanvas } from './builder-canvas'
import { PropertyPanel } from './property-panel'
import { BuilderHeader } from './builder-header'
import { useUIBuilderStore } from '@/lib/store'
import { UIComponent } from '@/types'
import { generateId } from '@/lib/utils'

export function UIBuilder() {
  const {
    currentProject,
    selectedComponent,
    draggedComponent,
    isPreviewMode,
    setDraggedComponent,
    addComponent,
    updateComponent,
  } = useUIBuilderStore()

  const [activeId, setActiveId] = useState<string | null>(null)

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    setActiveId(active.id as string)
    
    // If dragging from palette, create a new component
    if (active.data.current?.fromPalette) {
      const componentType = active.data.current.componentType
      const newComponent: UIComponent = {
        id: generateId(),
        project_id: currentProject?.id || '',
        type: componentType,
        props: getDefaultProps(componentType),
        position: { x: 0, y: 0 },
        size: { width: 200, height: 50 },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      setDraggedComponent(newComponent)
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setActiveId(null)
    setDraggedComponent(null)

    if (!over) return

    // If dropping on canvas
    if (over.id === 'canvas' && draggedComponent) {
      const rect = (over.rect as any)
      const position = {
        x: event.delta.x,
        y: event.delta.y,
      }
      
      addComponent({
        ...draggedComponent,
        position,
      })
    }
  }

  const getDefaultProps = (componentType: string) => {
    switch (componentType) {
      case 'button':
        return { text: 'Button', variant: 'default', size: 'md' }
      case 'input':
        return { placeholder: 'Enter text...', type: 'text' }
      case 'text':
        return { content: 'Text content', fontSize: 'base' }
      case 'heading':
        return { content: 'Heading', level: 1 }
      default:
        return {}
    }
  }

  if (isPreviewMode) {
    return (
      <div className="h-screen bg-white">
        <BuilderHeader />
        <div className="h-full pt-16">
          {/* Preview mode content */}
          <div className="h-full flex items-center justify-center">
            <Card className="p-8">
              <h2 className="text-2xl font-bold mb-4">Preview Mode</h2>
              <p className="text-muted-foreground">
                Your UI will be rendered here in preview mode
              </p>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        <BuilderHeader />
        
        <div className="flex-1 flex overflow-hidden">
          {/* Component Palette */}
          <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <ComponentPalette />
          </div>

          {/* Main Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-auto">
              <BuilderCanvas />
            </div>
          </div>

          {/* Property Panel */}
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
            <PropertyPanel />
          </div>
        </div>
      </div>

      <DragOverlay>
        {activeId && draggedComponent ? (
          <div className="bg-blue-100 border-2 border-blue-300 rounded p-2 opacity-80">
            <Badge variant="secondary">{draggedComponent.type}</Badge>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
