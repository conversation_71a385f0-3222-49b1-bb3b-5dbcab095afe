'use client'

import { useState } from 'react'
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCenter } from '@dnd-kit/core'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ComponentPalette } from './component-palette'
import { BuilderCanvas } from './builder-canvas'
import { PropertyPanel } from './property-panel'
import { BuilderHeader } from './builder-header'
import { useUIBuilderStore } from '@/lib/store'
import { UIComponent } from '@/types'
import { generateId } from '@/lib/utils'

export function UIBuilder() {
  const {
    currentProject,
    selectedComponent,
    draggedComponent,
    isPreviewMode,
    setDraggedComponent,
    addComponent,
    updateComponent,
  } = useUIBuilderStore()

  const [activeId, setActiveId] = useState<string | null>(null)

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    setActiveId(active.id as string)
    
    // If dragging from palette, create a new component
    if (active.data.current?.fromPalette) {
      const componentType = active.data.current.componentType
      const newComponent: UIComponent = {
        id: generateId(),
        project_id: currentProject?.id || '',
        type: componentType,
        props: getDefaultProps(componentType),
        position: { x: 0, y: 0 },
        size: { width: 200, height: 50 },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      setDraggedComponent(newComponent)
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setActiveId(null)
    setDraggedComponent(null)

    if (!over) return

    // If dropping on canvas
    if (over.id === 'canvas' && draggedComponent) {
      // Get the canvas element to calculate relative position
      const canvasElement = document.querySelector('[data-canvas="true"]')
      if (canvasElement) {
        const canvasRect = canvasElement.getBoundingClientRect()
        const position = {
          x: Math.max(0, event.activatorEvent.clientX - canvasRect.left - 100), // Offset for component width
          y: Math.max(0, event.activatorEvent.clientY - canvasRect.top - 25),   // Offset for component height
        }

        addComponent({
          ...draggedComponent,
          position,
        })
      } else {
        // Fallback to simple positioning
        addComponent({
          ...draggedComponent,
          position: { x: 50, y: 50 },
        })
      }
    }
  }

  const getDefaultProps = (componentType: string) => {
    switch (componentType) {
      case 'button':
        return { text: 'Button', variant: 'default', size: 'md' }
      case 'input':
        return { placeholder: 'Enter text...', type: 'text' }
      case 'text':
        return { content: 'Text content', fontSize: 'base' }
      case 'heading':
        return { content: 'Heading', level: 1 }
      default:
        return {}
    }
  }

  const renderPreviewComponent = (component: UIComponent) => {
    switch (component.type) {
      case 'button':
        return (
          <Button
            variant={component.props.variant || 'default'}
            size={component.props.size || 'default'}
            className="w-full"
          >
            {component.props.text || 'Button'}
          </Button>
        )

      case 'input':
        return (
          <input
            placeholder={component.props.placeholder || 'Enter text...'}
            type={component.props.type || 'text'}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        )

      case 'textarea':
        return (
          <textarea
            placeholder={component.props.placeholder || 'Enter text...'}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={component.props.rows || 3}
          />
        )

      case 'text':
        return (
          <p
            className={`text-${component.props.fontSize || 'base'} ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Text content'}
          </p>
        )

      case 'heading':
        const HeadingTag = `h${component.props.level || 1}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag
            className={`font-bold ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Heading'}
          </HeadingTag>
        )

      case 'card':
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{component.props.title || 'Card Title'}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{component.props.content || 'Card content goes here...'}</p>
            </CardContent>
          </Card>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input type="checkbox" id={component.id} className="rounded" />
            <label htmlFor={component.id}>
              {component.props.label || 'Checkbox label'}
            </label>
          </div>
        )

      default:
        return (
          <div className="border border-gray-300 rounded p-2 bg-gray-100">
            <Badge variant="secondary">{component.type}</Badge>
          </div>
        )
    }
  }

  if (isPreviewMode) {
    return (
      <div className="h-screen bg-white">
        <BuilderHeader />
        <div className="h-full pt-16 overflow-auto">
          {/* Preview mode content - render actual components */}
          <div className="h-full p-4">
            <div className="max-w-4xl mx-auto bg-white min-h-[600px] relative">
              {currentProject?.components && currentProject.components.length > 0 ? (
                currentProject.components.map((component) => (
                  <div
                    key={component.id}
                    style={{
                      position: 'absolute',
                      left: component.position.x,
                      top: component.position.y,
                      width: component.size.width,
                      minHeight: component.size.height,
                    }}
                  >
                    {renderPreviewComponent(component)}
                  </div>
                ))
              ) : (
                <div className="h-full flex items-center justify-center">
                  <Card className="p-8">
                    <h2 className="text-2xl font-bold mb-4">Preview Mode</h2>
                    <p className="text-muted-foreground">
                      Add components to see them rendered here
                    </p>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        <BuilderHeader />
        
        <div className="flex-1 flex overflow-hidden">
          {/* Component Palette */}
          <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <ComponentPalette />
          </div>

          {/* Main Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-auto">
              <BuilderCanvas />
            </div>
          </div>

          {/* Property Panel */}
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
            <PropertyPanel />
          </div>
        </div>
      </div>

      <DragOverlay>
        {activeId && draggedComponent ? (
          <div className="bg-blue-100 border-2 border-blue-300 rounded p-2 opacity-80">
            <Badge variant="secondary">{draggedComponent.type}</Badge>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
