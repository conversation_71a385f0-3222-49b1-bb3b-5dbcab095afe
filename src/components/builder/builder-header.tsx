'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useUIBuilderStore } from '@/lib/store'
import { useRouter } from 'next/navigation'
import {
  Eye,
  EyeOff,
  Save,
  Undo,
  Redo,
  Settings,
  Share,
  ArrowLeft,
  Globe
} from 'lucide-react'

export function BuilderHeader() {
  const { 
    currentProject, 
    isPreviewMode, 
    togglePreviewMode,
    setCurrentProject 
  } = useUIBuilderStore()
  const router = useRouter()

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving project...', currentProject)
    alert('🚧 Save functionality coming soon!\n\nThis will save the project to the database.')
  }

  const handleShare = () => {
    if (!currentProject || currentProject.components.length === 0) {
      alert('Please add some components to your project before sharing.')
      return
    }

    // Generate a shareable link
    const projectData = {
      name: currentProject.name,
      components: currentProject.components,
      timestamp: Date.now()
    }

    // Encode the project data
    const encodedData = btoa(JSON.stringify(projectData))
    const shareUrl = `${window.location.origin}/preview/${encodedData}`

    // Copy to clipboard
    navigator.clipboard.writeText(shareUrl).then(() => {
      alert(`🎉 Share link copied to clipboard!\n\n${shareUrl}\n\nAnyone with this link can view your project.`)
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = shareUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert(`🎉 Share link created!\n\n${shareUrl}\n\nLink has been copied to clipboard.`)
    })
  }

  const handlePublish = () => {
    if (!currentProject || currentProject.components.length === 0) {
      alert('Please add some components to your project before publishing.')
      return
    }

    // Generate a public URL
    const projectData = {
      name: currentProject.name,
      components: currentProject.components,
      timestamp: Date.now(),
      published: true
    }

    // Encode the project data
    const encodedData = btoa(JSON.stringify(projectData))
    const publicUrl = `${window.location.origin}/p/${encodedData}`

    // Copy to clipboard and show success
    navigator.clipboard.writeText(publicUrl).then(() => {
      alert(`🚀 Project published successfully!\n\nPublic URL: ${publicUrl}\n\nYour project is now live and accessible to anyone with this link. The URL has been copied to your clipboard.`)
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = publicUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert(`🚀 Project published successfully!\n\nPublic URL: ${publicUrl}\n\nYour project is now live and accessible to anyone.`)
    })
  }

  const handleSettings = () => {
    // TODO: Implement settings functionality
    alert('🚧 Settings functionality coming soon!\n\nThis will open project settings including:\n• Theme configuration\n• Webhook settings\n• Publishing options')
  }

  const handleUndo = () => {
    // TODO: Implement undo functionality
    alert('🚧 Undo functionality coming soon!')
  }

  const handleRedo = () => {
    // TODO: Implement redo functionality
    alert('🚧 Redo functionality coming soon!')
  }

  const handleBackToDashboard = () => {
    router.push('/dashboard')
  }

  // Create a default project if none exists
  if (!currentProject) {
    setCurrentProject({
      id: 'temp-project',
      name: 'Untitled Project',
      description: 'A new FlowUI project',
      is_published: false,
      components: [],
    })
  }

  return (
    <header className="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBackToDashboard}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-indigo-600 rounded"></div>
          <span className="font-semibold">FlowUI</span>
        </div>

        <div className="text-sm text-muted-foreground">
          {currentProject?.name || 'Untitled Project'}
        </div>
      </div>

      {/* Center Section - Tools */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleUndo}
          disabled
          className="flex items-center gap-1"
        >
          <Undo className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRedo}
          disabled
          className="flex items-center gap-1"
        >
          <Redo className="w-4 h-4" />
        </Button>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

        <Button
          variant={isPreviewMode ? "default" : "ghost"}
          size="sm"
          onClick={togglePreviewMode}
          className="flex items-center gap-2"
        >
          {isPreviewMode ? (
            <>
              <EyeOff className="w-4 h-4" />
              Edit
            </>
          ) : (
            <>
              <Eye className="w-4 h-4" />
              Preview
            </>
          )}
        </Button>
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="text-xs">
          {currentProject?.components?.length || 0} components
        </Badge>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleSettings}
          className="flex items-center gap-1"
        >
          <Settings className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleShare}
          className="flex items-center gap-2"
        >
          <Share className="w-4 h-4" />
          Share
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handlePublish}
          className="flex items-center gap-2"
        >
          <Globe className="w-4 h-4" />
          Publish
        </Button>

        <Button
          size="sm"
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <Save className="w-4 h-4" />
          Save
        </Button>
      </div>
    </header>
  )
}
