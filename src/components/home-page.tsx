'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

export function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg"></div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">FlowUI</span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/auth/signin">
              <Button variant="ghost">Sign In</Button>
            </Link>
            <Link href="/auth/signup">
              <Button>Get Started</Button>
            </Link>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-12">
        <div className="text-center max-w-4xl mx-auto">
          <Badge className="mb-4" variant="secondary">
            🚀 Now in Development
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Turn your <span className="text-blue-600">N8N automations</span> into beautiful web apps
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Transform your N8N automation workflows into beautiful, interactive web interfaces without code. 
            Build professional UIs in minutes, not hours.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/signup">
              <Button size="lg" className="text-lg px-8">
                Start Building
              </Button>
            </Link>
            <Link href="/auth/signin">
              <Button size="lg" variant="outline" className="text-lg px-8">
                View Demo
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mt-20">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎨 Visual UI Builder
              </CardTitle>
              <CardDescription>
                Drag & drop components with real-time preview
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Component palette with draggable elements</li>
                <li>• Real-time collision detection</li>
                <li>• Responsive design preview</li>
                <li>• Undo/redo functionality</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🔗 N8N Integration
              </CardTitle>
              <CardDescription>
                Seamless webhook connectivity and data mapping
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Easy webhook URL configuration</li>
                <li>• Request/response mapping</li>
                <li>• Data transformation tools</li>
                <li>• Error handling configuration</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🧪 API Testing Suite
              </CardTitle>
              <CardDescription>
                Postman-like functionality built-in
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• HTTP method selection</li>
                <li>• Headers and body management</li>
                <li>• Response inspector</li>
                <li>• Test collections</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📅 Scheduling Engine
              </CardTitle>
              <CardDescription>
                Advanced trigger management system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Cron-based scheduling</li>
                <li>• Manual triggers</li>
                <li>• Execution history</li>
                <li>• Failure notifications</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🌐 Public Publishing
              </CardTitle>
              <CardDescription>
                Share your interfaces with the world
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• One-click publish to public URL</li>
                <li>• Custom subdomain support</li>
                <li>• Password protection</li>
                <li>• Analytics and insights</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                👥 Team Collaboration
              </CardTitle>
              <CardDescription>
                Multi-tenant workspace management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Team invitations</li>
                <li>• Role-based access control</li>
                <li>• Project sharing</li>
                <li>• Workspace management</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Development Status */}
        <div className="mt-20 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>🚧 Development Progress</CardTitle>
              <CardDescription>
                FlowUI is currently in active development. Here's what we're working on:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-left">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">✅ Complete</Badge>
                  <span className="text-sm">Project Foundation Setup</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">🔄 In Progress</Badge>
                  <span className="text-sm">Authentication System</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">📋 Planned</Badge>
                  <span className="text-sm">Database Schema Implementation</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">📋 Planned</Badge>
                  <span className="text-sm">Core UI Components Library</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">📋 Planned</Badge>
                  <span className="text-sm">Drag & Drop Engine</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 mt-20 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center text-gray-600 dark:text-gray-400">
          <p>&copy; 2024 FlowUI. Built with Next.js, Tailwind CSS, and Shadcn/ui.</p>
        </div>
      </footer>
    </div>
  )
}
