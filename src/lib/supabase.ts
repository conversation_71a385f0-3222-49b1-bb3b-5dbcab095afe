import { createClient } from '@supabase/supabase-js'
import { createBrowserClient, createServerClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const createClientComponentClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Legacy client for backward compatibility (client-side only)
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types (will be generated later)
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          created_at: string
          subscription_tier: string
        }
        Insert: {
          id?: string
          email: string
          created_at?: string
          subscription_tier?: string
        }
        Update: {
          id?: string
          email?: string
          created_at?: string
          subscription_tier?: string
        }
      }
      // More tables will be added as we implement them
    }
  }
}
