import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

// User and Authentication Store
interface User {
  id: string
  email: string
  subscription_tier: 'free' | 'pro' | 'team' | 'enterprise'
}

interface AuthState {
  user: User | null
  isLoading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  logout: () => void
}

export const useAuthStore = create<AuthState>()(
  devtools(
    (set) => ({
      user: null,
      isLoading: true,
      setUser: (user) => set({ user }),
      setLoading: (isLoading) => set({ isLoading }),
      logout: () => set({ user: null }),
    }),
    { name: 'auth-store' }
  )
)

// UI Builder Store
interface UIComponent {
  id: string
  type: string
  props: Record<string, any>
  position: { x: number; y: number }
  size: { width: number; height: number }
  parent_id?: string
  children?: string[]
}

interface Project {
  id: string
  name: string
  description?: string
  webhook_url?: string
  is_published: boolean
  public_url?: string
  components: UIComponent[]
}

interface UIBuilderState {
  currentProject: Project | null
  selectedComponent: string | null
  draggedComponent: UIComponent | null
  isPreviewMode: boolean
  setCurrentProject: (project: Project | null) => void
  setSelectedComponent: (componentId: string | null) => void
  setDraggedComponent: (component: UIComponent | null) => void
  togglePreviewMode: () => void
  addComponent: (component: UIComponent) => void
  updateComponent: (componentId: string, updates: Partial<UIComponent>) => void
  removeComponent: (componentId: string) => void
}

export const useUIBuilderStore = create<UIBuilderState>()(
  devtools(
    (set, get) => ({
      currentProject: null,
      selectedComponent: null,
      draggedComponent: null,
      isPreviewMode: false,
      setCurrentProject: (project) => set({ currentProject: project }),
      setSelectedComponent: (componentId) => set({ selectedComponent: componentId }),
      setDraggedComponent: (component) => set({ draggedComponent: component }),
      togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),
      addComponent: (component) =>
        set((state) => {
          if (!state.currentProject) return state
          return {
            currentProject: {
              ...state.currentProject,
              components: [...state.currentProject.components, component],
            },
          }
        }),
      updateComponent: (componentId, updates) =>
        set((state) => {
          if (!state.currentProject) return state
          return {
            currentProject: {
              ...state.currentProject,
              components: state.currentProject.components.map((comp) =>
                comp.id === componentId ? { ...comp, ...updates } : comp
              ),
            },
          }
        }),
      removeComponent: (componentId) =>
        set((state) => {
          if (!state.currentProject) return state
          return {
            currentProject: {
              ...state.currentProject,
              components: state.currentProject.components.filter((comp) => comp.id !== componentId),
            },
          }
        }),
    }),
    { name: 'ui-builder-store' }
  )
)

// API Testing Store
interface APIRequest {
  id: string
  name: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  headers: Record<string, string>
  body?: string
}

interface APITestingState {
  requests: APIRequest[]
  currentRequest: APIRequest | null
  isLoading: boolean
  lastResponse: any
  addRequest: (request: APIRequest) => void
  updateRequest: (requestId: string, updates: Partial<APIRequest>) => void
  removeRequest: (requestId: string) => void
  setCurrentRequest: (request: APIRequest | null) => void
  setLoading: (loading: boolean) => void
  setLastResponse: (response: any) => void
}

export const useAPITestingStore = create<APITestingState>()(
  devtools(
    persist(
      (set) => ({
        requests: [],
        currentRequest: null,
        isLoading: false,
        lastResponse: null,
        addRequest: (request) =>
          set((state) => ({ requests: [...state.requests, request] })),
        updateRequest: (requestId, updates) =>
          set((state) => ({
            requests: state.requests.map((req) =>
              req.id === requestId ? { ...req, ...updates } : req
            ),
          })),
        removeRequest: (requestId) =>
          set((state) => ({
            requests: state.requests.filter((req) => req.id !== requestId),
          })),
        setCurrentRequest: (request) => set({ currentRequest: request }),
        setLoading: (isLoading) => set({ isLoading }),
        setLastResponse: (lastResponse) => set({ lastResponse }),
      }),
      {
        name: 'api-testing-storage',
        partialize: (state) => ({ requests: state.requests }),
      }
    ),
    { name: 'api-testing-store' }
  )
)
