'use client'

import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

export default function DebugPage() {
  const { user, isLoading, isAuthenticated } = useAuth()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🔍 Debug Information</h1>
        
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Authentication State</CardTitle>
              <CardDescription>Current authentication status and user information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <strong>Is Loading:</strong>
                  <Badge variant={isLoading ? "destructive" : "secondary"} className="ml-2">
                    {isLoading ? "True" : "False"}
                  </Badge>
                </div>
                <div>
                  <strong>Is Authenticated:</strong>
                  <Badge variant={isAuthenticated ? "default" : "outline"} className="ml-2">
                    {isAuthenticated ? "True" : "False"}
                  </Badge>
                </div>
              </div>
              
              <div>
                <strong>User Data:</strong>
                <pre className="mt-2 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-sm overflow-auto">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Environment Variables</CardTitle>
              <CardDescription>Check if environment variables are properly configured</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <strong>Supabase URL:</strong>
                <Badge variant={process.env.NEXT_PUBLIC_SUPABASE_URL ? "default" : "destructive"} className="ml-2">
                  {process.env.NEXT_PUBLIC_SUPABASE_URL ? "✅ Set" : "❌ Missing"}
                </Badge>
              </div>
              <div>
                <strong>Supabase Anon Key:</strong>
                <Badge variant={process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "default" : "destructive"} className="ml-2">
                  {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "✅ Set" : "❌ Missing"}
                </Badge>
              </div>
              {process.env.NEXT_PUBLIC_SUPABASE_URL && (
                <div className="mt-2">
                  <strong>Supabase URL:</strong>
                  <code className="ml-2 text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {process.env.NEXT_PUBLIC_SUPABASE_URL}
                  </code>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Test various authentication actions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <Button onClick={() => window.location.href = '/auth/signin'}>
                  Go to Sign In
                </Button>
                <Button onClick={() => window.location.href = '/auth/signup'}>
                  Go to Sign Up
                </Button>
                <Button onClick={() => window.location.href = '/dashboard'}>
                  Go to Dashboard
                </Button>
                <Button onClick={() => window.location.href = '/'}>
                  Go to Home
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Browser Information</CardTitle>
              <CardDescription>Browser and environment details</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><strong>User Agent:</strong> {navigator.userAgent}</div>
                <div><strong>URL:</strong> {window.location.href}</div>
                <div><strong>Local Storage Keys:</strong> {Object.keys(localStorage).join(', ') || 'None'}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
