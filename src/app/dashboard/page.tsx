'use client'

import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loading } from '@/components/loading'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function DashboardPage() {
  const { user, isLoading, signOut, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    console.log('🎯 Dashboard state:', { isLoading, isAuthenticated, user: user?.email })
    if (!isLoading && !isAuthenticated) {
      console.log('🔄 Redirecting to signin...')
      router.push('/auth/signin')
    }
  }, [isLoading, isAuthenticated, router, user])

  // Temporarily bypass auth check for testing
  if (isLoading) {
    console.log('🔄 Still loading...')
    return <Loading />
  }

  // For testing, let's show the dashboard even without auth
  // if (!isAuthenticated) {
  //   return null // Will redirect to signin
  // }

  const handleSignOut = async () => {
    await signOut()
  }

  const handleNewProject = () => {
    // Navigate directly to the builder for now
    router.push('/builder')
  }

  const handleAPITesting = () => {
    // For now, show an alert. Later this will navigate to API testing page
    alert('🚧 API Testing feature coming soon!\n\nThis will open the API testing suite with:\n• Request builder\n• Response inspector\n• Test collections\n• Postman-like functionality')
  }

  const handleSchedules = () => {
    // For now, show an alert. Later this will navigate to schedules page
    alert('🚧 Scheduling feature coming soon!\n\nThis will open the scheduling system with:\n• Cron-based scheduling\n• Manual triggers\n• Execution history\n• Failure notifications')
  }

  const handleCreateFirstProject = () => {
    router.push('/builder')
  }

  // For testing, create a fallback user if none exists
  const displayUser = user || {
    email: '<EMAIL>',
    subscription_tier: 'free'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800" suppressHydrationWarning>
        {/* Header */}
        <header className="border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg"></div>
                <span className="text-xl font-bold">FlowUI</span>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">{displayUser?.subscription_tier}</Badge>
                <span className="text-sm text-muted-foreground">{displayUser?.email}</span>
                <Button variant="outline" onClick={handleSignOut}>
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back!
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Ready to build some amazing interfaces? Let's get started.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎨 Create New Project
              </CardTitle>
              <CardDescription>
                Start building a new UI interface from scratch
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" onClick={handleNewProject}>
                New Project
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🧪 API Testing
              </CardTitle>
              <CardDescription>
                Test your N8N webhooks and API endpoints
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={handleAPITesting}>
                Open Tester
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📅 Schedules
              </CardTitle>
              <CardDescription>
                Manage your automation schedules and triggers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={handleSchedules}>
                View Schedules
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Projects */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Recent Projects</h2>
          <Card>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium mb-2">No projects yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first project to start building amazing interfaces
                </p>
                <Button onClick={handleCreateFirstProject}>
                  Create Your First Project
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">Projects</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">Published Pages</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">API Requests</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">Schedules</p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
