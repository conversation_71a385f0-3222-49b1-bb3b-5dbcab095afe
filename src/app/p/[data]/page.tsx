'use client'

import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, Heart } from 'lucide-react'
import Link from 'next/link'

interface UIComponent {
  id: string
  type: string
  props: Record<string, any>
  position: { x: number; y: number }
  size: { width: number; height: number }
}

interface ProjectData {
  name: string
  components: UIComponent[]
  timestamp: number
  published?: boolean
}

export default function PublishedPage() {
  const params = useParams()
  const [projectData, setProjectData] = useState<ProjectData | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    try {
      const encodedData = params.data as string
      const decodedData = atob(encodedData)
      const data = JSON.parse(decodedData) as ProjectData
      setProjectData(data)
    } catch (err) {
      setError('Invalid or corrupted link')
    }
  }, [params.data])

  const renderComponent = (component: UIComponent) => {
    switch (component.type) {
      case 'button':
        return (
          <Button 
            variant={component.props.variant || 'default'}
            size={component.props.size || 'default'}
            className="w-full"
          >
            {component.props.text || 'Button'}
          </Button>
        )

      case 'input':
        return (
          <input
            placeholder={component.props.placeholder || 'Enter text...'}
            type={component.props.type || 'text'}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        )

      case 'textarea':
        return (
          <textarea
            placeholder={component.props.placeholder || 'Enter text...'}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={component.props.rows || 3}
          />
        )

      case 'text':
        return (
          <p 
            className={`text-${component.props.fontSize || 'base'} ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Text content'}
          </p>
        )

      case 'heading':
        const HeadingTag = `h${component.props.level || 1}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag 
            className={`font-bold ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Heading'}
          </HeadingTag>
        )

      case 'card':
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{component.props.title || 'Card Title'}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{component.props.content || 'Card content goes here...'}</p>
            </CardContent>
          </Card>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input type="checkbox" id={component.id} className="rounded" />
            <label htmlFor={component.id}>
              {component.props.label || 'Checkbox label'}
            </label>
          </div>
        )

      case 'select':
        return (
          <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>Select an option</option>
            {component.props.options?.map((option: any, index: number) => (
              <option key={index} value={option.value}>{option.label}</option>
            ))}
          </select>
        )

      default:
        return (
          <div className="border border-gray-300 rounded p-2 bg-gray-100">
            <Badge variant="secondary">{component.type}</Badge>
          </div>
        )
    }
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="p-8 max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">{error}</p>
            <Link href="/">
              <Button>Go to FlowUI</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!projectData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Content */}
      <main className="p-4">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white min-h-[600px] relative">
            {projectData.components.map((component) => (
              <div
                key={component.id}
                style={{
                  position: 'absolute',
                  left: component.position.x,
                  top: component.position.y,
                  width: component.size.width,
                  minHeight: component.size.height,
                }}
              >
                {renderComponent(component)}
              </div>
            ))}
            
            {projectData.components.length === 0 && (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <p className="text-gray-500">This project has no components.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Floating FlowUI Badge */}
      <div className="fixed bottom-4 right-4">
        <Link href="/">
          <Button 
            size="sm" 
            className="shadow-lg hover:shadow-xl transition-shadow bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            <Heart className="w-4 h-4 mr-2" />
            Made with FlowUI
          </Button>
        </Link>
      </div>
    </div>
  )
}
