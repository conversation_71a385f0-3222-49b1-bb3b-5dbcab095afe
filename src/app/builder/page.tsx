'use client'

import { useAuth } from '@/hooks/useAuth'
import { Loading } from '@/components/loading'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { UIBuilder } from '@/components/builder/ui-builder'

export default function BuilderPage() {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()

  // Temporarily disable auth check for testing
  // useEffect(() => {
  //   if (!isLoading && !isAuthenticated) {
  //     router.push('/auth/signin')
  //   }
  // }, [isLoading, isAuthenticated, router])

  // if (isLoading) {
  //   return <Loading />
  // }

  // if (!isAuthenticated) {
  //   return null // Will redirect to signin
  // }

  return <UIBuilder />
}
