import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "FlowUI - Turn N8N automations into beautiful web apps",
  description: "Transform your N8N automation workflows into beautiful, interactive web interfaces without code. Build professional UIs in minutes, not hours.",
  keywords: ["N8N", "automation", "UI builder", "no-code", "workflow", "web app"],
  authors: [{ name: "FlowUI Team" }],
  openGraph: {
    title: "FlowUI - Turn N8N automations into beautiful web apps",
    description: "Transform your N8N automation workflows into beautiful, interactive web interfaces without code.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
