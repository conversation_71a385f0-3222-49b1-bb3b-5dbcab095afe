{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  let response = NextResponse.next({\n    request: {\n      headers: request.headers,\n    },\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          response = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            response.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Refresh session if expired - required for Server Components\n  const { data: { session } } = await supabase.auth.getSession()\n\n  // Protected routes that require authentication\n  const protectedRoutes = ['/dashboard', '/builder', '/api-testing', '/scheduling']\n  const authRoutes = ['/auth/signin', '/auth/signup']\n  \n  const isProtectedRoute = protectedRoutes.some(route => \n    request.nextUrl.pathname.startsWith(route)\n  )\n  const isAuthRoute = authRoutes.some(route => \n    request.nextUrl.pathname.startsWith(route)\n  )\n\n  // Redirect to signin if accessing protected route without session\n  if (isProtectedRoute && !session) {\n    const redirectUrl = new URL('/auth/signin', request.url)\n    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)\n    return NextResponse.redirect(redirectUrl)\n  }\n\n  // Redirect to dashboard if accessing auth routes with active session\n  if (isAuthRoute && session) {\n    return NextResponse.redirect(new URL('/dashboard', request.url))\n  }\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,IAAI,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC/B,SAAS;YACP,SAAS,QAAQ,OAAO;QAC1B;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,SAAS,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAEtC;QACF;IACF;IAGF,8DAA8D;IAC9D,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAE5D,+CAA+C;IAC/C,MAAM,kBAAkB;QAAC;QAAc;QAAY;QAAgB;KAAc;IACjF,MAAM,aAAa;QAAC;QAAgB;KAAe;IAEnD,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAEtC,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,kEAAkE;IAClE,IAAI,oBAAoB,CAAC,SAAS;QAChC,MAAM,cAAc,IAAI,IAAI,gBAAgB,QAAQ,GAAG;QACvD,YAAY,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO,CAAC,QAAQ;QACnE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,qEAAqE;IACrE,IAAI,eAAe,SAAS;QAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}