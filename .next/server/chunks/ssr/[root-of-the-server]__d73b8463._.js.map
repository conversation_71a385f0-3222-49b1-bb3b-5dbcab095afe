{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/home-page.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport Link from \"next/link\"\n\nexport function HomePage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Header */}\n      <header className=\"container mx-auto px-4 py-6\">\n        <nav className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg\"></div>\n            <span className=\"text-xl font-bold text-gray-900 dark:text-white\">FlowUI</span>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/auth/signin\">\n              <Button variant=\"ghost\">Sign In</Button>\n            </Link>\n            <Link href=\"/auth/signup\">\n              <Button>Get Started</Button>\n            </Link>\n          </div>\n        </nav>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"container mx-auto px-4 py-12\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <Badge className=\"mb-4\" variant=\"secondary\">\n            🚀 Now in Development\n          </Badge>\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6\">\n            Turn your <span className=\"text-blue-600\">N8N automations</span> into beautiful web apps\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Transform your N8N automation workflows into beautiful, interactive web interfaces without code. \n            Build professional UIs in minutes, not hours.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/auth/signup\">\n              <Button size=\"lg\" className=\"text-lg px-8\">\n                Start Building\n              </Button>\n            </Link>\n            <Link href=\"/auth/signin\">\n              <Button size=\"lg\" variant=\"outline\" className=\"text-lg px-8\">\n                View Demo\n              </Button>\n            </Link>\n          </div>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-3 gap-8 mt-20\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                🎨 Visual UI Builder\n              </CardTitle>\n              <CardDescription>\n                Drag & drop components with real-time preview\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                <li>• Component palette with draggable elements</li>\n                <li>• Real-time collision detection</li>\n                <li>• Responsive design preview</li>\n                <li>• Undo/redo functionality</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                🔗 N8N Integration\n              </CardTitle>\n              <CardDescription>\n                Seamless webhook connectivity and data mapping\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                <li>• Easy webhook URL configuration</li>\n                <li>• Request/response mapping</li>\n                <li>• Data transformation tools</li>\n                <li>• Error handling configuration</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                🧪 API Testing Suite\n              </CardTitle>\n              <CardDescription>\n                Postman-like functionality built-in\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                <li>• HTTP method selection</li>\n                <li>• Headers and body management</li>\n                <li>• Response inspector</li>\n                <li>• Test collections</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                📅 Scheduling Engine\n              </CardTitle>\n              <CardDescription>\n                Advanced trigger management system\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                <li>• Cron-based scheduling</li>\n                <li>• Manual triggers</li>\n                <li>• Execution history</li>\n                <li>• Failure notifications</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                🌐 Public Publishing\n              </CardTitle>\n              <CardDescription>\n                Share your interfaces with the world\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                <li>• One-click publish to public URL</li>\n                <li>• Custom subdomain support</li>\n                <li>• Password protection</li>\n                <li>• Analytics and insights</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                👥 Team Collaboration\n              </CardTitle>\n              <CardDescription>\n                Multi-tenant workspace management\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                <li>• Team invitations</li>\n                <li>• Role-based access control</li>\n                <li>• Project sharing</li>\n                <li>• Workspace management</li>\n              </ul>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Development Status */}\n        <div className=\"mt-20 text-center\">\n          <Card className=\"max-w-2xl mx-auto\">\n            <CardHeader>\n              <CardTitle>🚧 Development Progress</CardTitle>\n              <CardDescription>\n                FlowUI is currently in active development. Here's what we're working on:\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3 text-left\">\n                <div className=\"flex items-center gap-2\">\n                  <Badge variant=\"secondary\">✅ Complete</Badge>\n                  <span className=\"text-sm\">Project Foundation Setup</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Badge variant=\"outline\">🔄 In Progress</Badge>\n                  <span className=\"text-sm\">Authentication System</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Badge variant=\"outline\">📋 Planned</Badge>\n                  <span className=\"text-sm\">Database Schema Implementation</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Badge variant=\"outline\">📋 Planned</Badge>\n                  <span className=\"text-sm\">Core UI Components Library</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Badge variant=\"outline\">📋 Planned</Badge>\n                  <span className=\"text-sm\">Drag & Drop Engine</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"container mx-auto px-4 py-8 mt-20 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"text-center text-gray-600 dark:text-gray-400\">\n          <p>&copy; 2024 FlowUI. Built with Next.js, Tailwind CSS, and Shadcn/ui.</p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;sCAEpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAQ;;;;;;;;;;;8CAE1B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;gCAAO,SAAQ;0CAAY;;;;;;0CAG5C,8OAAC;gCAAG,WAAU;;oCAAoE;kDACtE,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAsB;;;;;;;0CAElE,8OAAC;gCAAE,WAAU;0CAAkE;;;;;;0CAI/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAe;;;;;;;;;;;kDAI7C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;kCAQnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { HomePage } from '@/components/home-page'\n\nexport default function Home() {\n  return <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,kIAAA,CAAA,WAAQ;;;;;AAClB", "debugId": null}}]}