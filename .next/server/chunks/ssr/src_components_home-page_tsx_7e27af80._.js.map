{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/home-page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomePage = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePage() from the server but HomePage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home-page.tsx <module evaluation>\",\n    \"HomePage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8DACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/home-page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomePage = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePage() from the server but HomePage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home-page.tsx\",\n    \"HomePage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0CACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}