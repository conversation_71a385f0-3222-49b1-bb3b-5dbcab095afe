{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/loading.tsx"], "sourcesContent": ["interface LoadingProps {\n  message?: string\n}\n\nexport function Loading({ message = \"Loading...\" }: LoadingProps) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-muted-foreground\">{message}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAIO,SAAS,QAAQ,EAAE,UAAU,YAAY,EAAgB;IAC9D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/page.tsx"], "sourcesContent": ["import dynamic from 'next/dynamic'\nimport { Loading } from '@/components/loading'\n\nconst HomePage = dynamic(() => import('@/components/home-page').then(mod => ({ default: mod.HomePage })), {\n  ssr: false,\n  loading: () => <Loading />\n})\n\nexport default function Home() {\n  return <HomePage />\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,oJAAiC,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,QAAQ;QAAC,CAAC;;;;;;IACpG,KAAK;IACL,SAAS,kBAAM,8OAAC,6HAAA,CAAA,UAAO;;;;;;AAGV,SAAS;IACtB,qBAAO,8OAAC;;;;;AACV", "debugId": null}}]}