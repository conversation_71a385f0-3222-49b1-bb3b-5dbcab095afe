module.exports = {

"[project]/src/components/home-page.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_home-page_tsx_7e27af80._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/home-page.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}),

};