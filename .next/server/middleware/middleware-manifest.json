{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_895135be.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XBnWTXQTpZuckHnYdyfrhWX2ugBIK4p1ITOuQN7l/Wo=", "__NEXT_PREVIEW_MODE_ID": "117bc55283089b32203c3cd11228811a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f813d8897229fc63589ed6a16e4b163686bba5836141d2fe36e700d9ecce56b0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d38ac0d7744477f1d40719c9fc9f31842ec809e296bf951be36e90a4d4906166"}}}, "instrumentation": null, "functions": {}}