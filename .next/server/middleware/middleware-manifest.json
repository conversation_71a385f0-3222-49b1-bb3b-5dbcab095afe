{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_895135be.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XBnWTXQTpZuckHnYdyfrhWX2ugBIK4p1ITOuQN7l/Wo=", "__NEXT_PREVIEW_MODE_ID": "4b6607b6f21d6442b7e1cfd7fd581be3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "35c4f477d5d4c3c340720ab247ba664e5293859000d9b3598e841a77a68cf758", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "70781d38955987115bef7fc0c5955f1b6cbfed32618ffa0193dff14df1dc8d65"}}}, "instrumentation": null, "functions": {}}